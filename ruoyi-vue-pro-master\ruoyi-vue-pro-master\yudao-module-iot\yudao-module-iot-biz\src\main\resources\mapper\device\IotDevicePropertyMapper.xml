<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.iot.dal.tdengine.IotDevicePropertyMapper">

    <select id="getProductPropertySTableFieldList" resultType="cn.iocoder.yudao.module.iot.framework.tdengine.core.TDengineTableField">
        DESCRIBE product_property_${productKey}
    </select>

    <update id="createProductPropertySTable">
        CREATE STABLE product_property_${productKey} (
            ts TIMESTAMP,
            report_time TIMESTAMP,
        <foreach item="field" collection="fields" separator=",">
            ${field.field} ${field.type}
            <if test="field.length != null and field.length > 0">
                (${field.length})
            </if>
        </foreach>
        )
        TAGS (
            device_key NCHAR(50)
        )
    </update>

    <update id="alterProductPropertySTableAddField">
        ALTER STABLE product_property_${productKey}
        ADD COLUMN ${field.field} ${field.type}
        <if test="field.length != null and field.length > 0">
            (${field.length})
        </if>
    </update>

    <update id="alterProductPropertySTableModifyField">
        ALTER STABLE product_property_${productKey}
        MODIFY COLUMN ${field.field} ${field.type}
        <if test="field.length != null and field.length > 0">
            (${field.length})
        </if>
    </update>

    <update id="alterProductPropertySTableDropField">
        ALTER STABLE product_property_${productKey}
        DROP COLUMN ${field.field}
    </update>

    <insert id="insert">
        INSERT INTO device_property_${device.deviceKey}
        USING product_property_${device.productKey}
        TAGS ('${device.deviceKey}')
        (ts, report_time,
        <foreach item="key" collection="properties.keys" separator=",">
            ${@cn.hutool.core.util.StrUtil@toUnderlineCase(key)}
        </foreach>
        )
        VALUES
        (NOW, #{reportTime},
        <foreach item="value" collection="properties.values" separator=",">
            #{value}
        </foreach>
        )
    </insert>

    <select id="describeSuperTable" resultType="java.util.Map">
        DESCRIBE product_property_${productKey}
    </select>

    <select id="selectPageByHistory" resultType="cn.iocoder.yudao.module.iot.controller.admin.device.vo.data.IotDevicePropertyRespVO">
        SELECT ${@cn.hutool.core.util.StrUtil@toUnderlineCase(reqVO.identifier)} AS `value`, ts AS update_time
        FROM device_property_${reqVO.deviceKey}
        WHERE ${@cn.hutool.core.util.StrUtil@toUnderlineCase(reqVO.identifier)} IS NOT NULL
          AND ts BETWEEN ${@cn.hutool.core.date.LocalDateTimeUtil@toEpochMilli(reqVO.times[0])}
              AND ${@cn.hutool.core.date.LocalDateTimeUtil@toEpochMilli(reqVO.times[1])}
        ORDER BY ts DESC
    </select>

</mapper>