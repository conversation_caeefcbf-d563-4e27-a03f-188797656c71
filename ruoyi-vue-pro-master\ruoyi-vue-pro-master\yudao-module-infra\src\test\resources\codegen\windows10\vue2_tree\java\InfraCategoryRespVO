package cn.iocoder.yudao.module.infra.controller.admin.demo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import cn.idev.excel.annotation.*;

@Schema(description = "管理后台 - 分类 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InfraCategoryRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "名字", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋头")
    @ExcelProperty("名字")
    private String name;

    @Schema(description = "父编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "2048")
    @ExcelProperty("父编号")
    private Long parentId;

}
