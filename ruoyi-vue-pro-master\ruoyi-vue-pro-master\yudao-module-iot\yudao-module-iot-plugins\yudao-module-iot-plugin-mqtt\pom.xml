<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="
         http://maven.apache.org/POM/4.0.0
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>yudao-module-iot-plugins</artifactId>
        <groupId>cn.iocoder.boot</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>

    <artifactId>yudao-module-iot-plugin-mqtt</artifactId>

    <name>${project.artifactId}</name>
    <!-- TODO @芋艿：待整理 -->
    <description>
        物联网 插件模块 - mqtt 插件
    </description>

    <properties>
        <!-- 插件相关 -->
        <plugin.id>mqtt-plugin</plugin.id>
        <plugin.class>cn.iocoder.yudao.module.iot.plugin.MqttPlugin</plugin.class>
        <plugin.version>0.0.1</plugin.version>
        <plugin.provider>ahh</plugin.provider>
        <plugin.description>mqtt-plugin-0.0.1</plugin.description>
        <plugin.dependencies/>
    </properties>

    <build>
        <plugins>
            <!-- DOESN'T WORK WITH MAVEN 3 (I defined the plugin metadata in properties section)
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>properties-maven-plugin</artifactId>
                <version>1.0-alpha-2</version>
                <executions>
                  <execution>
                    <phase>initialize</phase>
                    <goals>
                      <goal>read-project-properties</goal>
                    </goals>
                    <configuration>
                      <files>
                        <file>plugin.properties</file>
                      </files>
                    </configuration>
                  </execution>
                </executions>
            </plugin>
            -->

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>1.6</version>
                <executions>
                    <execution>
                        <id>unzip jar file</id>
                        <phase>package</phase>
                        <configuration>
                            <target>
                                <unzip src="target/${project.artifactId}-${project.version}.${project.packaging}"
                                       dest="target/plugin-classes"/>
                            </target>
                        </configuration>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>2.3</version>
                <configuration>
                    <descriptors>
                        <descriptor>
                            src/main/assembly/assembly.xml
                        </descriptor>
                    </descriptors>
                    <appendAssemblyId>false</appendAssemblyId>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>attached</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>2.4</version>
                <configuration>
                    <archive>
                        <manifestEntries>
                            <Plugin-Id>${plugin.id}</Plugin-Id>
                            <Plugin-Class>${plugin.class}</Plugin-Class>
                            <Plugin-Version>${plugin.version}</Plugin-Version>
                            <Plugin-Provider>${plugin.provider}</Plugin-Provider>
                            <Plugin-Description>${plugin.description}</Plugin-Description>
                            <Plugin-Dependencies>${plugin.dependencies}</Plugin-Dependencies>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>

            <plugin>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <!-- 其他依赖项 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <!-- PF4J Spring 集成 -->
        <dependency>
            <groupId>org.pf4j</groupId>
            <artifactId>pf4j-spring</artifactId>
            <scope>provided</scope>
        </dependency>
        <!-- 项目依赖 -->
        <dependency>
            <groupId>cn.iocoder.boot</groupId>
            <artifactId>yudao-module-iot-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <scope>provided</scope>
        </dependency>
        <!-- Vert.x MQTT -->
        <dependency>
            <groupId>io.vertx</groupId>
            <artifactId>vertx-mqtt</artifactId>
            <version>4.5.11</version>
        </dependency>
    </dependencies>
</project>