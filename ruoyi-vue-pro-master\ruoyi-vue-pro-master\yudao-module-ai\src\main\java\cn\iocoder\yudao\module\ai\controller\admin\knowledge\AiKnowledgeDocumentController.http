### 创建知识文档
POST {{baseUrl}}/ai/knowledge/document/create
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{adminTenantId}}

{
  "knowledgeId": 2,
  "name": "测试文档",
  "url": "https://static.iocoder.cn/README.md",
  "segmentMaxTokens": 800
}

### 批量创建知识文档
POST {{baseUrl}}/ai/knowledge/document/create-list
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{adminTenantId}}

{
  "knowledgeId": 1,
  "list": [
    {
      "name": "测试文档1",
      "url": "https://static.iocoder.cn/README.md",
      "segmentMaxTokens": 800
    },
    {
      "name": "测试文档2",
      "url": "https://static.iocoder.cn/README_yudao.md",
      "segmentMaxTokens": 400
    }
  ]
}

