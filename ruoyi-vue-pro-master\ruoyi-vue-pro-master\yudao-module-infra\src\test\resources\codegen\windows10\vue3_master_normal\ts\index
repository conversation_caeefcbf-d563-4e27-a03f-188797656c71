import request from '@/config/axios'

export interface StudentVO {
  id: number
  name: string
  description: string
  birthday: Date
  sex: number
  enabled: boolean
  avatar: string
  video: string
  memo: string
}

// 查询学生分页
export const getStudentPage = async (params) => {
  return await request.get({ url: `/infra/student/page`, params })
}

// 查询学生详情
export const getStudent = async (id: number) => {
  return await request.get({ url: `/infra/student/get?id=` + id })
}

// 新增学生
export const createStudent = async (data: StudentVO) => {
  return await request.post({ url: `/infra/student/create`, data })
}

// 修改学生
export const updateStudent = async (data: StudentVO) => {
  return await request.put({ url: `/infra/student/update`, data })
}

// 删除学生
export const deleteStudent = async (id: number) => {
  return await request.delete({ url: `/infra/student/delete?id=` + id })
}

// 导出学生 Excel
export const exportStudent = async (params) => {
  return await request.download({ url: `/infra/student/export-excel`, params })
}

// ==================== 子表（学生联系人） ====================

// 获得学生联系人列表
export const getStudentContactListByStudentId = async (studentId) => {
  return await request.get({ url: `/infra/student/student-contact/list-by-student-id?studentId=` + studentId })
}

// ==================== 子表（学生班主任） ====================

// 获得学生班主任
export const getStudentTeacherByStudentId = async (studentId) => {
  return await request.get({ url: `/infra/student/student-teacher/get-by-student-id?studentId=` + studentId })
}
