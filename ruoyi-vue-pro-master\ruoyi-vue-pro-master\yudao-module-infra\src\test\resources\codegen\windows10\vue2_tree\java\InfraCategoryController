package cn.iocoder.yudao.module.infra.controller.admin.demo;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.yudao.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.infra.controller.admin.demo.vo.*;
import cn.iocoder.yudao.module.infra.dal.dataobject.demo.InfraCategoryDO;
import cn.iocoder.yudao.module.infra.service.demo.InfraCategoryService;

@Tag(name = "管理后台 - 分类")
@RestController
@RequestMapping("/infra/category")
@Validated
public class InfraCategoryController {

    @Resource
    private InfraCategoryService categoryService;

    @PostMapping("/create")
    @Operation(summary = "创建分类")
    @PreAuthorize("@ss.hasPermission('infra:category:create')")
    public CommonResult<Long> createCategory(@Valid @RequestBody InfraCategorySaveReqVO createReqVO) {
        return success(categoryService.createCategory(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新分类")
    @PreAuthorize("@ss.hasPermission('infra:category:update')")
    public CommonResult<Boolean> updateCategory(@Valid @RequestBody InfraCategorySaveReqVO updateReqVO) {
        categoryService.updateCategory(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除分类")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('infra:category:delete')")
    public CommonResult<Boolean> deleteCategory(@RequestParam("id") Long id) {
        categoryService.deleteCategory(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得分类")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('infra:category:query')")
    public CommonResult<InfraCategoryRespVO> getCategory(@RequestParam("id") Long id) {
        InfraCategoryDO category = categoryService.getCategory(id);
        return success(BeanUtils.toBean(category, InfraCategoryRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得分类列表")
    @PreAuthorize("@ss.hasPermission('infra:category:query')")
    public CommonResult<List<InfraCategoryRespVO>> getCategoryList(@Valid InfraCategoryListReqVO listReqVO) {
        List<InfraCategoryDO> list = categoryService.getCategoryList(listReqVO);
        return success(BeanUtils.toBean(list, InfraCategoryRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出分类 Excel")
    @PreAuthorize("@ss.hasPermission('infra:category:export')")
    @OperateLog(type = EXPORT)
    public void exportCategoryExcel(@Valid InfraCategoryListReqVO listReqVO,
              HttpServletResponse response) throws IOException {
        List<InfraCategoryDO> list = categoryService.getCategoryList(listReqVO);
        // 导出 Excel
        ExcelUtils.write(response, "分类.xls", "数据", InfraCategoryRespVO.class,
                        BeanUtils.toBean(list, InfraCategoryRespVO.class));
    }

}