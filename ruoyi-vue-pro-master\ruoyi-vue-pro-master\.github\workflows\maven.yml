# This workflow will build a Java project with <PERSON><PERSON>, and cache/restore any dependencies to improve the workflow execution time
# For more information see: https://help.github.com/actions/language-and-framework-guides/building-and-testing-java-with-maven

name: Java CI with <PERSON><PERSON>

on:
  push:
    branches: [ master ]
  # pull_request:
  #   branches: [ master ]

jobs:
  build:

    runs-on: ubuntu-latest

    strategy:
      matrix:
        java: [ '8', '11', '17' ]

    steps:
    - uses: actions/checkout@v2
    - name: Set up JDK ${{ matrix.Java }}
      uses: actions/setup-java@v2
      with:
        java-version: ${{ matrix.java }}
        distribution: 'temurin'
        cache: maven
    - name: Build with Maven
      run: mvn -B package --file pom.xml -Dmaven.test.skip=true
