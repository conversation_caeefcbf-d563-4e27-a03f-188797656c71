<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>
    <artifactId>yudao-module-ai</artifactId>

    <name>${project.artifactId}</name>
    <description>
        ai 模块下，接入 LLM 大模型，支持聊天、绘图、音乐、写作、思维导图等功能。
        目前已接入各种模型，不限于：
          国内：通义千问、文心一言、讯飞星火、智谱 GLM、DeepSeek
          国外：OpenAI、Ollama、Midjourney、StableDiffusion、Suno
    </description>
    <properties>
        <spring-ai.version>1.0.0</spring-ai.version>
        <alibaba-ai.version>*******</alibaba-ai.version>
        <tinyflow.version>1.0.2</tinyflow.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>cn.iocoder.boot</groupId>
            <artifactId>yudao-module-system</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>cn.iocoder.boot</groupId>
            <artifactId>yudao-module-infra</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 业务组件 -->

        <dependency>
            <groupId>cn.iocoder.boot</groupId>
            <artifactId>yudao-spring-boot-starter-biz-tenant</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>cn.iocoder.boot</groupId>
            <artifactId>yudao-spring-boot-starter-security</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>cn.iocoder.boot</groupId>
            <artifactId>yudao-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <!-- Job 相关 -->
        <dependency>
            <groupId>cn.iocoder.boot</groupId>
            <artifactId>yudao-spring-boot-starter-job</artifactId>
        </dependency>

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>cn.iocoder.boot</groupId>
            <artifactId>yudao-spring-boot-starter-test</artifactId>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>cn.iocoder.boot</groupId>
            <artifactId>yudao-spring-boot-starter-excel</artifactId>
        </dependency>

        <!-- Spring AI Model 模型接入 -->
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-starter-model-openai</artifactId>
            <version>${spring-ai.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-starter-model-azure-openai</artifactId>
            <version>${spring-ai.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-starter-model-deepseek</artifactId>
            <version>${spring-ai.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-starter-model-ollama</artifactId>
            <version>${spring-ai.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-starter-model-stability-ai</artifactId>
            <version>${spring-ai.version}</version>
        </dependency>
        <dependency>
            <!-- 智谱 GLM -->
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-starter-model-zhipuai</artifactId>
            <version>${spring-ai.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-starter-model-minimax</artifactId>
            <version>${spring-ai.version}</version>
        </dependency>

        <dependency>
            <!-- 通义千问 -->
            <groupId>com.alibaba.cloud.ai</groupId>
            <artifactId>spring-ai-alibaba-starter-dashscope</artifactId>
            <version>${alibaba-ai.version}</version>
        </dependency>

        <dependency>
            <!-- 文心一言 -->
            <groupId>org.springaicommunity</groupId>
            <artifactId>qianfan-spring-boot-starter</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <!-- 月之暗灭 -->
            <groupId>org.springaicommunity</groupId>
            <artifactId>moonshot-spring-boot-starter</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!-- 向量存储：https://db-engines.com/en/ranking/vector+dbms -->
        <dependency>
            <!-- Qdrant：https://qdrant.tech/ -->
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-starter-vector-store-qdrant</artifactId>
            <version>${spring-ai.version}</version>
        </dependency>

        <dependency>
            <!-- Redis：https://redis.io/docs/latest/develop/get-started/vector-database/ -->
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-starter-vector-store-redis</artifactId>
            <version>${spring-ai.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.iocoder.boot</groupId>
            <artifactId>yudao-spring-boot-starter-redis</artifactId>
        </dependency>

        <dependency>
            <!-- Milvus：https://milvus.io/ -->
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-starter-vector-store-milvus</artifactId>
            <version>${spring-ai.version}</version>
            <exclusions>
                <!-- 解决和 logback 的日志冲突 -->
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-reload4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <!-- Tika：负责内容的解析 -->
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-tika-document-reader</artifactId>
            <version>${spring-ai.version}</version>
            <!-- TODO 芋艿：boot 项目里，不引入 cloud 依赖！！！另外，这样也是为了解决启动报错的问题！ -->
            <exclusions>
                <exclusion>
                    <artifactId>spring-cloud-function-context</artifactId>
                    <groupId>org.springframework.cloud</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-cloud-function-core</artifactId>
                    <groupId>org.springframework.cloud</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- TinyFlow：AI 工作流 -->
        <dependency>
            <groupId>dev.tinyflow</groupId>
            <artifactId>tinyflow-java-core</artifactId>
            <version>${tinyflow.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.jfinal</groupId>
                    <artifactId>enjoy</artifactId>
                </exclusion>
                <exclusion>
                    <!-- 解决 https://gitee.com/zhijiantianya/ruoyi-vue-pro/pulls/1318/ 问题 -->
                    <groupId>com.agentsflex</groupId>
                    <artifactId>agents-flex-store-elasticsearch</artifactId>
                </exclusion>
                <exclusion>
                    <!-- TODO @芋艿：暂时移除 groovy，和 iot 冲突 -->
                    <groupId>org.codehaus.groovy</groupId>
                    <artifactId>groovy-all</artifactId>
                </exclusion>
                <!-- 解决和 logback 的日志冲突 -->
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-simple</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-slf4j-impl</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-reload4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

</project>