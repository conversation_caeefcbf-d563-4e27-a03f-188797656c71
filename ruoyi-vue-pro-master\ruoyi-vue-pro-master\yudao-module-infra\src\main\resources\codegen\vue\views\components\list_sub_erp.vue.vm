#set ($subTable = $subTables.get($subIndex))##当前表
#set ($subColumns = $subColumnsList.get($subIndex))##当前字段数组
#set ($subJoinColumn = $subJoinColumns.get($subIndex))##当前 join 字段
#set ($subSimpleClassName = $subSimpleClassNames.get($subIndex))
#set ($subJoinColumn = $subJoinColumns.get($subIndex))##当前 join 字段
#set ($SubJoinColumnName = $subJoinColumn.javaField.substring(0,1).toUpperCase() + ${subJoinColumn.javaField.substring(1)})##首字母大写
<template>
  <div class="app-container">
#if ($table.templateType == 11)
    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="openForm(undefined)"
                   v-hasPermi="['${permissionPrefix}:create']">新增</el-button>
      </el-col>
    #if ($deleteBatchEnable)
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="isEmpty(checkedIds)"
            @click="handleDeleteBatch"
            v-hasPermi="['${permissionPrefix}:delete']"
        >
          批量删除
        </el-button>
      </el-col>
    #end
    </el-row>
#end
      ## 列表
      <el-table
          v-loading="loading"
          :data="list"
          :stripe="true"
          :show-overflow-tooltip="true"
          #if ($table.templateType == 11 && $deleteBatchEnable)
          @selection-change="handleRowCheckboxChange"
          #end
      >
          #if ($table.templateType == 11 && $deleteBatchEnable)
            <el-table-column type="selection" width="55" />
          #end
          #foreach($column in $subColumns)
              #if ($column.listOperationResult)
                  #set ($dictType=$column.dictType)
                  #set ($javaField = $column.javaField)
                  #set ($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
                  #set ($comment=$column.columnComment)
                  #if ( $column.id == $subJoinColumn.id) ## 特殊：忽略主子表的 join 字段，不用填写
                  #elseif ($column.javaType == "LocalDateTime")## 时间类型
                <el-table-column label="${comment}" align="center" prop="${javaField}" width="180">
                  <template v-slot="scope">
                    <span>{{ parseTime(scope.row.${javaField}) }}</span>
                  </template>
                </el-table-column>
                  #elseif($column.dictType && "" != $column.dictType)## 数据字典
                <el-table-column label="${comment}" align="center" prop="${javaField}">
                  <template v-slot="scope">
                    <dict-tag :type="DICT_TYPE.$dictType.toUpperCase()" :value="scope.row.${column.javaField}" />
                  </template>
                </el-table-column>
              #else
                <el-table-column label="${comment}" align="center" prop="${javaField}" />
              #end
          #end
      #end
    <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
      <template v-slot="scope">
        <el-button size="mini" type="text" icon="el-icon-edit" @click="openForm(scope.row.${primaryColumn.javaField})"
                   v-hasPermi="['${permissionPrefix}:update']">修改</el-button>
        <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                   v-hasPermi="['${permissionPrefix}:delete']">删除</el-button>
      </template>
    </el-table-column>
  </el-table>
#if ($table.templateType == 11)
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>
  <!-- 对话框(添加 / 修改) -->
  <${subSimpleClassName}Form ref="formRef" @success="getList" />
#end
  </div>
</template>

<script>
  import * as ${simpleClassName}Api from '@/api/${table.moduleName}/${table.businessName}';
  #if ($table.templateType == 11)
  import ${subSimpleClassName}Form from './${subSimpleClassName}Form.vue';
  #end
  export default {
    name: "${subSimpleClassName}List",
#if ($table.templateType == 11)
    components: {
       ${subSimpleClassName}Form
    },
#end
    props:[
      '${subJoinColumn.javaField}'
    ],// ${subJoinColumn.columnComment}（主表的关联字段）
    data() {
      return {
        // 遮罩层
        loading: true,
        // 列表的数据
        list: [],
#if ($table.templateType == 11)
        #if ($deleteBatchEnable)
        checkedIds: [],
        #end
        // 列表的总页数
        total: 0,
        // 查询参数
        queryParams: {
          pageNo: 1,
          pageSize: 10,
          ${subJoinColumn.javaField}: undefined
        }
#end
      };
    },
#if ($table.templateType != 11)
    created() {
      this.getList();
    },
#end
    watch:{/** 监听主表的关联字段的变化，加载对应的子表数据 */
        ${subJoinColumn.javaField}:{
            handler(val) {
              this.queryParams.${subJoinColumn.javaField} = val;
              if (val){
                this.handleQuery();
              }
            },
            immediate: true
      }
    },
    methods: {
      /** 查询列表 */
      async getList() {
        try {
          this.loading = true;
          #if ($table.templateType == 11)
            const res = await ${simpleClassName}Api.get${subSimpleClassName}Page(this.queryParams);
            this.list = res.data.list;
            this.total = res.data.total;
          #else
              #if ( $subTable.subJoinMany )
                const res = await ${simpleClassName}Api.get${subSimpleClassName}ListBy${SubJoinColumnName}(this.${subJoinColumn.javaField});
                this.list = res.data;
              #else
                const res = await  ${simpleClassName}Api.get${subSimpleClassName}By${SubJoinColumnName}(this.${subJoinColumn.javaField});
                const data = res.data;
                if (!data) {
                  return;
                }
                this.list.push(data);
              #end
          #end
        } finally {
          this.loading = false;
        }
      },
      #if ($table.templateType == 11 && $deleteBatchEnable)
        /** 批量删除${table.classComment} */
        async handleDeleteBatch() {
          await this.#[[$modal]]#.confirm('是否确认删除?')
          try {
            await ${simpleClassName}Api.delete${subSimpleClassName}List(this.checkedIds);
            await this.getList();
            this.#[[$modal]]#.msgSuccess("删除成功");
          } catch {}
        },
        handleRowCheckboxChange(records) {
          this.checkedIds = records.map((item) => item.id);
        },
        #end

#if ($table.templateType == 11)
        /** 搜索按钮操作 */
        handleQuery() {
          this.queryParams.pageNo = 1;
          this.getList();
        },
      /** 添加/修改操作 */
      openForm(id) {
        if (!this.${subJoinColumn.javaField}) {
          this.#[[$modal]]#.msgError('请选择一个${table.classComment}');
          return;
        }
        this.#[[$]]#refs["formRef"].open(id, this.${subJoinColumn.javaField});
      },
      /** 删除按钮操作 */
      async handleDelete(row) {
        const ${primaryColumn.javaField} = row.${primaryColumn.javaField};
        await this.#[[$modal]]#.confirm('是否确认删除${table.classComment}编号为"' + ${primaryColumn.javaField} + '"的数据项?');
        try {
          await ${simpleClassName}Api.delete${subSimpleClassName}(${primaryColumn.javaField});
          await this.getList();
          this.#[[$modal]]#.msgSuccess("删除成功");
        } catch {}
      },
#end
    }
  };
</script>
